spring:
  datasource:
    driver-class-name: dm.jdbc.driver.DmDriver
    url: ${DM_URL:jdbc:dm://BWTY_PRO/BWTY-CRAFT-PRO?zeroDateTimeBehavior=convertToNull&useUnicode=true&characterEncoding=utf-8}
    #数据库的使用用户
    username: ${DM_USERNAME:SYSDBA}
    #实际使用数据库的密码
    password: ${DM_PWD:Dameng123}
    hikari:
      #最大连接数
      maximum-pool-size: 500
      #最大超时时间
      connection-timeout: 600000
  jpa:
    properties:
      hibernate:
        dialect: org.hibernate.dialect.DmDialect
    show-sql: true
    open-in-view: true
    hibernate:
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
      #     ddl-auto: none
      ddl-auto: none
      # 此配置做了oracle配置，必须
    database-platform: org.hibernate.dialect.DmDialect
  flyway:
    # 默认不启用，true 为启用
    enabled: false
    baseline-on-migrate: true
    # baseline-version：产生的原因是兼容已经有版本发布的项目（即数据库中原本就存在一些表），要满足 3 个条件：
    # 1. baseline-on-migrate: true
    # 2. 数据库中已经存在其他表。
    # 3. flyway_schema_history 表不存在。
    # 当以上 3 个条件成立时，设置的 baseline-version 的值是多少，那么这个版本及之前版本的脚本都不会被执行。
    # 并且，flyway_schema_history 表中会多出第一条字段 script 为 << Flyway Baseline >> 的数据记录。
    # 不需要 baseline-version 的话可以注释掉。需要的话比如配置为：baseline-version: 2020.12.11
    baseline-version:
    # 禁用 placeholder replacement，否则 sql 脚本中不能写 ${} 这样的字符。
    placeholder-replacement: false
    # flyway脚本命名规则为：V<VERSION>__<NAME>.sql (with <VERSION> an underscore-separated version, such as ‘1’ or ‘2_1’)
    # flyway在spring boot中默认配置位置为：classpath:db/migration
    locations:
      - classpath:db/migration/dev
  cloud:
    stream:
      kafka:
        binder:
          brokers: ${KAFKA:***********:9092,***********:9092,***********:9092}
          auto-create-topics: true   # 自动创建topics
          required-acks: -1 #0：这意味着生产者producer不等待来自broker同步完成的确认继续发送下一条（批）消息。此选项提供最低的延迟但最弱的耐久性保证（当服务器发生故障时某些数据会丢失，如leader已死，但producer并不知情，发出去的信息broker就收不到）。
          #          # 1：这意味着producer在leader已成功收到的数据并得到确认后发送下一条message。此选项提供了更好的耐久性为客户等待服务器确认请求成功（被写入死亡leader但尚未复制将失去了唯一的消息）。
          #          #-1：这意味着producer在follower副本确认接收到数据后才算一次发送完成。 此选项提供最好的耐久性，我们保证没有信息将丢失，只要至少一个同步副本保持存活
          transaction:
            producer:
              buffer-size: 16384 #Kafka 生产者在发送之前尝试批处理的数据量的上限（以字节为单位）。
              batch-timeout: 100 #生产者在发送消息之前等待多长时间以允许在同一批次中累积更多消息,每批消息创建后最多等待多少毫秒必须发送出去，不管本批次装没装满
      binding-retry-interval: 30 #绑定重试间隔，默认30秒
      bindings:
        realtimeDataConsumer-in-0: # 消费者topic配置，bindingName命名规范：bean名称-in-索引，索引值为参数个数
          destination: realtime_data #相当于topic
          #          group: group_websocket_realtime_data #相当于group.id
          consumer:
            concurrency: 3 #消费并发数量,默认值1
            batchMode: true # 默认false,是否批量消费，false的话就会一条一条消费，如果是true的话在bean配置里面的参数应该是List<>
        transformGyckConsumer-in-0: # 消费者topic配置，bindingName命名规范：bean名称-in-索引，索引值为参数个数
          destination: gyck_craft #相当于topic(需要根据实际情况修改topic,这里只是一个示例)
          group: group_decode #相当于group.id
          consumer:
            concurrency: 3 #消费并发数量,默认值1
            batchMode: true
    function:
      definition: realtimeDataConsumer;transformGyckConsumer;
  kafka:
    consumer:
      enable-auto-commit: true #消费者的偏移量是否在后台定期提交。
      auto-offset-reset: latest #当Kafka中没有初始偏移量或服务器上不再存在当前偏移量时该怎么办。latest自动将offset偏移重置为最新偏移，从此偏移开始消费
      #      fetch-max-wait: 100ms
      #      fetch-min-size: 1
      max-poll-records: 1000 # 消费者一次拉取数据的最大条数
#  data:
#    redis:
#      host: ${RUOYI_REDIS_HOST:***************}
#      port: ${RUOYI_REDIS_PORT:31727}
#      password: ${RUOYI_REDIS_PASSWORD:}

  data:
    redis:
      cluster:
        nodes: ${CRAFT_REDIS_NODES:***********:6379,***********:6379,***********:6379}
        max-redirects: 3
      password: ${CRAFT_REDIS_PASSWORD:KyAbse2SvEu622da}
      database: ${CRAFT_REDIS_DATABASE:1}
# nebula 图数据库配置
nebula:
  db:
    url: ${NEBULA_URL:***********:9669,***********:9669,***********:9669}
    username: ${NEBULA_USERNAME:root}
    password: ${NEBULA_PWD:nebula}
    space:
      space_name: ${NEBULA_SPACE_NAME:bwty_pro}
      vid_type: ${NEBULA_SPACE_VID_TYPE:FIXED_STRING(128)}
      comment: ${NEBULA_SPACE_COMMENT:宝武特冶}


dynamic:
  mongo:
    enable-dynamic: true
    primary: primary
    datasource:
      primary:
        name: primary
        url: ${CRAFT_MONGO_URL:***********:27017,***********:27017,***********:27017}
        database: ${CRAFT_MONGO_DATABASE:bwty-craft-pro}
        username: ${CRAFT_MONGO_USERNAME:admin}
        password: ${CRAFT_MONGO_PWD:qbUgBCCCKZ47pI7i}
        authentication-database: admin

dataPoint:
  provider:
    name: ${DATA_POINT_PROVIDER:data-point}
    url: ${DATA_POINT_PROVIDER_URL:***************:31751}

jwt:
  secret: ${JWT_SECRET:""}
service:
  provider:
    name: ${RUOYI_SYSTEM:ruoyi-system}
    url: ${RUOYI_SYSTEM_URL:http://***************:32169}
    file:
      name: ${RUOYI_FILE:127.0.0.1}
      url: ${RUOYI_FILE_URL:127.0.0.1:9300}
casdoor:
  endpoint: ${CASDOOR_ENDPOINT:http://192.168.100.206:31968/api/casdoor/}
  client-id: ${CASDOOR_CLIENT_ID:501a2c41c780079b8e1c}
  client-secret: ${CASDOOR_CLIENT_SECRET:dc7b9e039882d84049bcf2d49a051416d1be1f30}
  organization-name: ${CASDOOR_ORGANIZATION:bici}
  application-name: ${CASDOOR_APPLICATION:data-platform}

server:
  servlet:
    context-path: /api/craft