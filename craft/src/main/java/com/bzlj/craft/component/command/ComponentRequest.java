package com.bzlj.craft.component.command;

import lombok.Data;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-13 14:37
 */
@Data
public class ComponentRequest {
    private String componentId;

    private String model;

    /**
     * 组件绑定的方法全路径
     */
    private String methodPath;

    /**
     * 组件绑定的方法参数
     */
    private String params;

    private boolean loop;

    /**
     * 轮询间隔 毫秒
     */
    private long loopInterval;

}
