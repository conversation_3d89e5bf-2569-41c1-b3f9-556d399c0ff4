package com.bzlj.craft.service.impl;


import cn.hutool.core.date.LocalDateTimeUtil;
import com.bzlj.base.repository.BaseRepository;
import com.bzlj.base.result.DataResult;
import com.bzlj.base.result.PageResult;
import com.bzlj.base.search.SearchCondition;
import com.bzlj.base.search.SearchItem;
import com.bzlj.base.search.SearchItems;
import com.bzlj.base.util.DTOConverter;
import com.bzlj.craft.api.service.*;
import com.bzlj.craft.component.command.ComponentSearch;
import com.bzlj.craft.datapoint.api.DataPointService;
import com.bzlj.craft.dto.*;
import com.bzlj.craft.entity.*;
import com.bzlj.craft.enums.ParamType;
import com.bzlj.craft.enums.PointMethodType;
import com.bzlj.craft.enums.PointType;
import com.bzlj.craft.enums.TeamRole;
import com.bzlj.craft.event.TaskStatusEventPublisher;
import com.bzlj.craft.mongo.entity.AbnormalRecord;
import com.bzlj.craft.mongo.entity.AlarmContent;
import com.bzlj.craft.mongo.entity.FormAttr;
import com.bzlj.craft.mongo.repository.AbnormalRecordRepository;
import com.bzlj.craft.mongo.repository.FormAttrRepository;
import com.bzlj.craft.repository.*;
import com.bzlj.craft.service.ISurveillanceService;
import com.bzlj.craft.util.JsonUtils;
import com.bzlj.craft.vo.surveillance.*;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.querydsl.core.types.Expression;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.CaseBuilder;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.annotation.Resource;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.bzlj.craft.entity.QProductionTask.productionTask;
import static com.bzlj.craft.enums.AlertType.manual;
import static com.bzlj.craft.enums.AlertType.system;
import static com.bzlj.craft.exception.BiciErrorData.TASK_ID_IS_NULL;
import static com.bzlj.craft.exception.BiciErrorData.TASK_NOT_EXIST;

/**
 * <AUTHOR>
 * @description: 在线测控
 * @date 2025-03-10 13:34
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class SurveillanceServiceImpl implements ISurveillanceService {

    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private ProductionTaskRepository repository;

    @Autowired
    private ProductionTaskExtendRepository productionTaskExtendRepository;

    @Autowired
    private ParameterDefinitionRepository parameterDefinitionRepository;

    @Autowired
    private IProcessParameterService processParameterService;
    @Autowired
    private ProductionTaskRepository productionTaskRepository;
    @Autowired
    private TaskEquipmentRepository taskEquipmentRepository;
    @Autowired
    private ParameterDefinitionExtendRepository parameterDefinitionExtendRepository;
    @Autowired
    private ProcessParameterRepository processParameterRepository;


    @Override
    public BaseRepository<ProductionTask, String> getRepository() {
        return repository;
    }

    @Override
    public EntityManager getEntityManager() {
        return entityManager;
    }

    @Override
    public Class<ProductionTaskDTO> getDTOClass() {
        return ProductionTaskDTO.class;
    }

    @Override
    public Class<ProductionTask> getPOClass() {
        return ProductionTask.class;
    }

    @Autowired
    JPAQueryFactory queryFactory;

    private static final QProductionTask qTask = productionTask;

    private static final String IN_PROGRESS_CODE = "in_progress";
    private static final String NOT_STARTED_CODE = "not_start";
    private static final String COMPLETED_CODE = "completed";

    @Autowired
    private ProcessStepRepository processStepRepository;

    @Autowired
    private WorkStepRepository workStepRepository;

    @Autowired
    private IOperationLogService operationLogService;

    @Autowired
    private EmployeeRepository employeeRepository;

    @Autowired
    private IQualityInspectionService qualityInspectionService;

    @Autowired
    private ITaskMaterialService taskMaterialService;

    @Autowired
    private IProduceReportLoadMaterialService produceReportLoadMaterialService;


    @Autowired
    private ShiftHandoverRepository shiftHandoverRepository;

    @Autowired
    private EmployeeTeamRepository employeeTeamRepository;

    @Autowired
    private IDataPointInfoService dataPointInfoService;

    @Autowired
    private AbnormalRecordRepository abnormalRecordRepository;

    @Resource
    DataPointService dataPointService;

    @Autowired
    private IProcessScoreDetailsService processScoreDetailsService;

    @Autowired
    private FormAttrRepository formAttrRepository;

    @Autowired
    private TaskStatusEventPublisher taskStatusEventPublisher;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProductionTaskDTO convertToDto(ProductionTask entity, Class<ProductionTaskDTO> dtoClass, String... ignoreProperties) {
        ProductionTaskDTO dto = createInstance(dtoClass);
        Set<TaskMaterial> taskMaterials = entity.getTaskMaterials();
        List<String> brands = new ArrayList<>();
        if (!CollectionUtils.isEmpty(taskMaterials)) {
            Map<Boolean, List<TaskMaterial>> relationMap = taskMaterials.stream().collect(Collectors.groupingBy(TaskMaterial::getRelationType));
            relationMap.forEach((relation, value) -> {
                List<Material> materials = value.stream().filter(taskMaterial -> Objects.nonNull(taskMaterial.getMaterial())).map(TaskMaterial::getMaterial).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(materials)) {
                    String materialCodes = materials.stream().map(Material::getMaterialCode).collect(Collectors.joining(","));
                    if (BooleanUtils.isTrue(relation)) {
                        dto.setOutputMaterialCode(materialCodes);
                    } else {
                        dto.setInputMaterialCode(materialCodes);
                    }
                    List<String> materialBrands = materials.stream().filter(material -> StringUtils.isNotEmpty(material.getBrand())).map(Material::getBrand).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(materialBrands)) {
                        brands.addAll(materialBrands);
                    }
                }
            });
        }
        if (!CollectionUtils.isEmpty(brands)) {
            dto.setBrand(String.join(",", brands.stream().distinct().toList()));
        }
        if (Objects.nonNull(entity.getProcess())) {
            dto.setProcessId(entity.getProcess().getId());
            if (Objects.nonNull(entity.getProcess().getProcessType())) {
                dto.setProcessType(entity.getProcess().getProcessType().getItemName());
                dto.setProcessTypeCode(entity.getProcess().getProcessType().getItemCode());
            }
            if (Objects.nonNull(entity.getProcess().getCraft())) {
                dto.setCraftCode(entity.getProcess().getCraft().getCraftCode());
            }
        }
        if (!CollectionUtils.isEmpty(entity.getTaskEquipments())) {
            List<Equipment> equipments = entity.getTaskEquipments().stream().filter(taskEquipment -> Objects.nonNull(taskEquipment.getEquip())).map(TaskEquipment::getEquip).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(equipments)) {
                String equipmentCodes = equipments.stream().map(Equipment::getCode).collect(Collectors.joining(","));
                String equipmentNames = equipments.stream().map(Equipment::getName).collect(Collectors.joining(","));
                dto.setEquipmentCode(equipmentCodes);
                dto.setEquipmentName(equipmentNames);
            }
        }
        dto.setPlanEndTime(entity.getPlanEndTime());
        dto.setPlanStartTime(entity.getPlanStartTime());

        dto.setTaskId(entity.getTaskId());
        dto.setTaskCode(entity.getTaskCode());
        dto.setStartTime(entity.getStartTime());
        dto.setEndTime(entity.getEndTime());
        dto.setPlanWeight(entity.getPlanWeight());
        dto.setPlanQuantity(entity.getPlanQuantity());
        dto.setScore(entity.getScore());
        if (Objects.nonNull(entity.getStatusCode())) {
            dto.setStatusCode(entity.getStatusCode().getItemCode());
        }
        return dto;
    }

    @Override
    public DataResult getSurveillanceList(String json) {
        SearchCondition searchCondition = JsonUtils.fromJson(json, SearchCondition.class);
        PageResult<ProductionTaskDTO> result = findByCondition(searchCondition);
        DataResult dataResult = new DataResult();
        dataResult.setTotal(result.getTotal());
        dataResult.setPageSize(result.getPageSize());
        dataResult.setPageCurrent(result.getPageCurrent());
        if (CollectionUtils.isEmpty(result.getContent())) {
            dataResult.setList(Lists.newArrayList());
            return dataResult;
        }
        List<List<Object>> value = DTOConverter.convert(result.getContent(), ProductionTaskVO.class);
        dataResult.setList(value);
        return dataResult;
    }


    @Override
    public DataResult getTaskStatusStatistics(String plantCode) {
        // 构建基础查询
        JPAQuery<TaskStatisticsDTO> query = queryFactory.select(
                        Projections.constructor(
                                TaskStatisticsDTO.class,
                                qTask.count(),
                                createStatusCountExpression(IN_PROGRESS_CODE),
                                createStatusCountExpression(NOT_STARTED_CODE),
                                createStatusCountExpression(COMPLETED_CODE)
                        ))
                .from(qTask);

        // 如果plantCode不为空，添加where条件
        if (StringUtils.isNotBlank(plantCode)) {
            query = query.where(qTask.plant.plantCode.eq(plantCode));
        }

        TaskStatisticsDTO taskStatisticsDTO = query.fetchOne();
        List<List<Object>> value = DTOConverter.convert(Lists.newArrayList(taskStatisticsDTO), TaskStatisticsVO.class);
        DataResult dataResult = new DataResult();
        dataResult.setList(value);
        return dataResult;
    }

    private Expression<Long> createStatusCountExpression(String statusCode) {
        return new CaseBuilder()
                .when(qTask.statusCode.itemCode.eq(statusCode))
                .then(1L)
                .otherwise(0L)
                .sum()
                .coalesce(0L) // 处理null值，转换为0
                .as(statusCode + "_count");
    }

    @Override
    @Transactional
    public DataResult findSurveillanceStep(String json) {
        SearchCondition searchCondition = JsonUtils.fromJson(json, SearchCondition.class);
        List<ProductionTaskDTO> tasks = findAllWithCondition(searchCondition);
        List<WorkStepDTO> workSteps = new ArrayList<>();
        if (!CollectionUtils.isEmpty(tasks)) {
            workSteps = findWorkStepByTask(tasks.getFirst().getTaskId(), tasks.getFirst().getProcessId());
        }
        DataResult dataResult = new DataResult();
        if (CollectionUtils.isEmpty(workSteps)) {
            dataResult.setList(Lists.newArrayList());
            return dataResult;
        }
        dataResult.setList(DTOConverter.convert(workSteps, WorkStepVO.class));
        return dataResult;
    }

    @Override
    public List<WorkStepDTO> findWorkStepByTask(String taskId, String processId) {
        if (Objects.nonNull(processId)) {
            List<ProcessStep> processSteps = processStepRepository.findByProcessIdOrderByStepOrderAsc(processId);
            //只查询原始工序上的执行工步单元
            List<String> stepIds = processSteps.stream().map(ProcessStep::getId).collect(Collectors.toList());
            List<WorkStep> workSteps = workStepRepository.findByStepIdInAndTaskTaskId(stepIds, taskId);
            workSteps.sort(Comparator.comparing(WorkStep::getWorkStepOrder));
            List<WorkStepDTO> steps = workSteps.stream().map(workStep -> {
                WorkStepDTO dto = new WorkStepDTO();
                dto.setId(workStep.getWorkStepId());
                dto.setStepId(workStep.getStep().getId());
                dto.setWorkStepName(workStep.getWorkStepName());
                dto.setStepCode(workStep.getStep().getStepCode());
                dto.setWorkStepOrder(workStep.getWorkStepOrder());
                dto.setStartTime(workStep.getStartTime());
                dto.setEndTime(workStep.getEndTime());
                if (Objects.nonNull(workStep.getStatusCode())) {
                    dto.setStatusCode(workStep.getStatusCode().getItemCode());
                }
                dto.setTaskId(taskId);
                return dto;
            }).collect(Collectors.toList());

            return steps;
        }
        return Lists.newArrayList();
    }


    @Override
    public DataResult findStepResultParams(String json) {
        DataResult dataResult = new DataResult();
        dataResult.setList(Lists.newArrayList());
        SearchCondition searchCondition = JsonUtils.fromJson(json, SearchCondition.class);
        List<ProductionTaskDTO> tasks = findAllWithCondition(searchCondition);
        if (!CollectionUtils.isEmpty(tasks)) {
            List<ResultParamsDTO> resultParams = findResultParams(tasks.getFirst().getTaskId(), tasks.getFirst().getProcessId());
            dataResult.setList(DTOConverter.convert(resultParams, ResultParamsVO.class));
        }

        return dataResult;
    }

    @Override
    public DataResult findOperation(String json) {
        DataResult dataResult = new DataResult();
        dataResult.setList(Lists.newArrayList());
        SearchCondition searchCondition = JsonUtils.fromJson(json, SearchCondition.class);
        searchCondition.setOpenProps(Lists.newArrayList("task", "workstep"));
        List<OperationLogDTO> operationLogDTOS = operationLogService.findAllWithCondition(searchCondition);
        if (!CollectionUtils.isEmpty(operationLogDTOS)) {
            List<String> operatorIds = operationLogDTOS.stream().filter(operationLogDTO -> Objects.nonNull(operationLogDTO.getOperatorId()))
                    .map(OperationLogDTO::getOperatorId).collect(Collectors.toList());
            Map<String, Employee> operatorMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(operatorIds)) {
                List<Employee> employees = employeeRepository.findAllById(operatorIds);
                operatorMap.putAll(Maps.uniqueIndex(employees, Employee::getId));
            }
            operationLogDTOS.forEach(operationLogDTO -> {
                Employee employee = operatorMap.get(operationLogDTO.getOperatorId());
                if (Objects.nonNull(employee)) {
                    operationLogDTO.setOperator(employee.getFullName());
                }
            });
            dataResult.setList(DTOConverter.convert(operationLogDTOS, OperationLogVO.class));
        }
        return dataResult;
    }

    @Override
    public DataResult findInspectionData(String json) {
        DataResult dataResult = new DataResult();
        dataResult.setList(Lists.newArrayList());
        SearchCondition searchCondition = JsonUtils.fromJson(json, SearchCondition.class);
        Map<String, Object> inspectionData = qualityInspectionService.findInspectionData(searchCondition);
        if (CollectionUtils.isEmpty(inspectionData)) return dataResult;
        Object tabs = inspectionData.get("tabNames");
        if (Objects.nonNull(tabs)) {
            dataResult.setTabNames((List) tabs);
        }
        Object data = inspectionData.get("data");
        if (Objects.nonNull(data)) {
            dataResult.setList((List) data);
        }
        return dataResult;
    }

    @Override
    public DataResult findLoadMaterialRecorde(String json) {
        DataResult dataResult = new DataResult();
        dataResult.setList(Lists.newArrayList());
        ComponentSearch componentSearch = JsonUtils.fromJson(json, ComponentSearch.class);
        SearchCondition searchCondition = componentSearch.getSearchCondition();
        // 如果有componentId，使用组件化查询
        if (StringUtils.isNotEmpty(componentSearch.getComponentKey())) {
            findLoadMaterialRecordeWithComponent(componentSearch.getComponentKey(), searchCondition, dataResult);
        } else {
            // 否则使用默认的VO查询
            findLoadMaterialRecordeWithVO(searchCondition, dataResult);
        }
        return dataResult;
    }

    private void findLoadMaterialRecordeWithComponent(String componentKey, SearchCondition searchCondition, DataResult dataResult) {
        //根据componentId查询表单属性
        List<FormAttr> formAttrs = formAttrRepository.findByComponentKeyOrderByOrder(componentKey);
        if (CollectionUtils.isEmpty(formAttrs)) {
            findLoadMaterialRecordeWithVO(searchCondition, dataResult);
            return;
        }
        searchCondition.setOpenProps(Lists.newArrayList("material", "material.materialAttrs", "material.materialAttrs.attrType"));
        List<ProduceReportLoadMaterial> produceReportLoadMaterials = produceReportLoadMaterialService.findAllEntityWithCondition(searchCondition);

        if (CollectionUtils.isEmpty(produceReportLoadMaterials)) {
            dataResult.setList(Lists.newArrayList());
            return;
        }

        // 根据formAttrs的dataIndex从taskMaterials中提取对应字段组装为链式数据
        List<List<Object>> chainedData = buildChainedDataWithReflection(produceReportLoadMaterials, formAttrs);
        dataResult.setList(chainedData);
    }

    /**
     * 根据VO构建加料记录
     *
     * @param searchCondition
     * @param dataResult
     */
    private void findLoadMaterialRecordeWithVO(SearchCondition searchCondition, DataResult dataResult) {
        searchCondition.setOpenProps(Lists.newArrayList("material", "createUser"));
        List<ProduceReportLoadMaterialDTO> produceReportLoadMaterials = produceReportLoadMaterialService.findAllWithCondition(searchCondition);
        if (!CollectionUtils.isEmpty(produceReportLoadMaterials)) {
            dataResult.setList(DTOConverter.convert(produceReportLoadMaterials, ProduceReportLoadMaterialVO.class));
        }
    }

    @Override
    public DataResult findProductionMaterials(String json) {
        DataResult dataResult = new DataResult();
        dataResult.setList(Lists.newArrayList());
        ComponentSearch componentSearch = JsonUtils.fromJson(json, ComponentSearch.class);
        SearchCondition searchCondition = componentSearch.getSearchCondition();

        // 如果有componentId，使用组件化查询
        if (StringUtils.isNotEmpty(componentSearch.getComponentKey())) {
            findProductionMaterialsWithComponent(componentSearch.getComponentKey(), searchCondition, dataResult);
        } else {
            // 否则使用默认的VO查询
            findProductionMaterialsWithVO(searchCondition, dataResult);
        }

        return dataResult;
    }

    /**
     * 根据配置属性构建查询结果
     *
     * @param componentKey
     * @param searchCondition
     * @param dataResult
     */
    private void findProductionMaterialsWithComponent(String componentKey, SearchCondition searchCondition, DataResult dataResult) {
        //根据componentId查询表单属性
        List<FormAttr> formAttrs = formAttrRepository.findByComponentKeyOrderByOrder(componentKey);
        if (CollectionUtils.isEmpty(formAttrs)) {
            findProductionMaterialsWithVO(searchCondition, dataResult);
            return;
        }
        searchCondition.setOpenProps(Lists.newArrayList("material", "material.materialAttrs", "material.materialAttrs.attrType"));
        List<TaskMaterial> taskMaterials = taskMaterialService.findAllEntityWithCondition(searchCondition);

        if (CollectionUtils.isEmpty(taskMaterials)) {
            dataResult.setList(Lists.newArrayList());
            return;
        }

        // 根据formAttrs的dataIndex从taskMaterials中提取对应字段组装为链式数据
        List<List<Object>> chainedData = buildChainedDataWithReflection(taskMaterials, formAttrs);
        dataResult.setList(chainedData);
    }

    /**
     * 根据VO对象生成材料信息
     *
     * @param searchCondition 查询条件对象，用于筛选材料信息
     * @param dataResult      数据结果对象，用于存储查询结果
     */
    private void findProductionMaterialsWithVO(SearchCondition searchCondition, DataResult dataResult) {
        searchCondition.setOpenProps(Lists.newArrayList("material"));
        List<TaskMaterialDTO> taskMaterialDTOS = taskMaterialService.findAllWithCondition(searchCondition);
        if (!CollectionUtils.isEmpty(taskMaterialDTOS)) {
            dataResult.setList(DTOConverter.convert(taskMaterialDTOS, TaskMaterialVO.class));
        }
    }


    /**
     * 使用反射方式构建链式数据（回退方案）
     *
     * @param data      任务物料列表
     * @param formAttrs 表单属性列表
     * @return 链式数据，格式为[["1","2"],["aa","bb"]]
     */
    private List<List<Object>> buildChainedDataWithReflection(List data, List<FormAttr> formAttrs) {
        List<List<Object>> result = new ArrayList<>();

        // 为每个formAttr创建一个列表
        for (FormAttr formAttr : formAttrs) {
            List<Object> columnData = new ArrayList<>();
            String dataIndex = formAttr.getDataIndex();

            // 从每个TaskMaterial中提取对应的字段值
            for (Object taskMaterial : data) {
                Object value = extractValueByPath(taskMaterial, dataIndex);
                if (value != null) {
                    columnData.add(value.toString());
                } else {
                    columnData.add(""); // 空值处理
                }
            }

            result.add(columnData);
        }

        return result;
    }

    /**
     * 根据属性路径从对象中提取值（反射方式）
     * 支持嵌套属性访问，如 "material.materialId", "material.materialAttrs.attr.ingotType"
     *
     * @param obj  源对象
     * @param path 属性路径，用点分隔
     * @return 提取的值，如果路径无效或值为null则返回null
     */
    private Object extractValueByPath(Object obj, String path) {
        if (obj == null || StringUtils.isEmpty(path)) {
            return null;
        }

        try {
            String[] pathParts = path.split("\\.");
            Object currentObj = obj;

            for (int i = 0; i < pathParts.length; i++) {
                if (currentObj == null) {
                    return null;
                }

                String currentPath = pathParts[i];

                // 特殊处理：如果当前路径是"materialAttrs"且对象是Material，需要处理集合
                if ("materialAttrs".equals(currentPath) && currentObj instanceof Material) {
                    Material material = (Material) currentObj;
                    Set<MaterialAttr> materialAttrs = material.getMaterialAttrs();
                    if (CollectionUtils.isEmpty(materialAttrs)) {
                        return null;
                    }

                    // 如果还有下一级路径，需要进一步处理
                    if (i + 1 < pathParts.length) {
                        String nextPath = pathParts[i + 1];
                        if ("attr".equals(nextPath) && i + 2 < pathParts.length) {
                            // 路径形如 "material.materialAttrs.attr.fieldName"
                            String fieldName = pathParts[i + 2];
                            // 从所有MaterialAttr的attr中查找指定字段
                            for (MaterialAttr attr : materialAttrs) {
                                Map<String, Object> attrMap = attr.getAttr();
                                if (attrMap != null && attrMap.containsKey(fieldName)) {
                                    return attrMap.get(fieldName);
                                }
                            }
                            return null;
                        }
                    }

                    // 返回第一个MaterialAttr（如果只是访问materialAttrs）
                    return materialAttrs.iterator().next();
                }

                // 特殊处理：如果当前路径是"attr"且前一个对象是MaterialAttr，需要从JSON字段中提取
                if ("attr".equals(currentPath) && currentObj instanceof MaterialAttr) {
                    MaterialAttr materialAttr = (MaterialAttr) currentObj;
                    Map<String, Object> attrMap = materialAttr.getAttr();
                    if (attrMap != null && i + 1 < pathParts.length) {
                        // 还有下一级路径，从JSON Map中提取
                        String nextPath = pathParts[i + 1];
                        return attrMap.get(nextPath);
                    } else {
                        // 没有下一级路径，返回整个attr Map
                        return attrMap;
                    }
                }

                // 使用BeanWrapper进行常规属性访问
                BeanWrapper beanWrapper = new BeanWrapperImpl(currentObj);
                if (beanWrapper.isReadableProperty(currentPath)) {
                    currentObj = beanWrapper.getPropertyValue(currentPath);
                } else {
                    return null;
                }
            }

            return currentObj;
        } catch (Exception e) {
            log.warn("提取属性值失败，路径: {}, 错误: {}", path, e.getMessage());
            return null;
        }
    }

    /**
     * 从JsonNode中根据属性路径提取值
     * 支持嵌套属性访问，如 "material.materialId", "material.materialAttrs.attr.ingotType"
     *
     * @param jsonNode JSON节点
     * @param path     属性路径，用点分隔
     * @return 提取的值，如果路径无效或值为null则返回null
     */
    private Object extractValueFromJsonNode(JsonNode jsonNode, String path) {
        if (jsonNode == null || StringUtils.isEmpty(path)) {
            return null;
        }

        try {
            String[] pathParts = path.split("\\.");
            JsonNode currentNode = jsonNode;

            for (int i = 0; i < pathParts.length; i++) {
                if (currentNode == null || currentNode.isNull()) {
                    return null;
                }

                String currentPath = pathParts[i];

                // 特殊处理：materialAttrs是数组，需要遍历查找
                if ("materialAttrs".equals(currentPath)) {
                    JsonNode materialAttrsNode = currentNode.get(currentPath);
                    if (materialAttrsNode == null || !materialAttrsNode.isArray()) {
                        return null;
                    }

                    // 如果还有下一级路径，需要进一步处理
                    if (i + 1 < pathParts.length) {
                        String nextPath = pathParts[i + 1];
                        if ("attr".equals(nextPath) && i + 2 < pathParts.length) {
                            // 路径形如 "material.materialAttrs.attr.fieldName"
                            String fieldName = pathParts[i + 2];
                            // 从所有MaterialAttr的attr中查找指定字段
                            for (JsonNode materialAttrNode : materialAttrsNode) {
                                JsonNode attrNode = materialAttrNode.get("attr");
                                if (attrNode != null && attrNode.has(fieldName)) {
                                    JsonNode fieldNode = attrNode.get(fieldName);
                                    return fieldNode.isTextual() ? fieldNode.asText() : fieldNode.toString();
                                }
                            }
                            return null;
                        }
                    }

                    // 如果只是访问materialAttrs，返回第一个元素
                    if (materialAttrsNode.size() > 0) {
                        currentNode = materialAttrsNode.get(0);
                    } else {
                        return null;
                    }
                } else {
                    // 常规属性访问
                    currentNode = currentNode.get(currentPath);
                }
            }

            if (currentNode == null || currentNode.isNull()) {
                return null;
            }

            // 根据节点类型返回相应的值
            if (currentNode.isTextual()) {
                return currentNode.asText();
            } else if (currentNode.isNumber()) {
                return currentNode.asText();
            } else if (currentNode.isBoolean()) {
                return currentNode.asBoolean();
            } else {
                return currentNode.toString();
            }

        } catch (Exception e) {
            log.warn("从JSON节点提取属性值失败，路径: {}, 错误: {}", path, e.getMessage());
            return null;
        }
    }


    @Override
    public DataResult findTeamInfo(String json) {
        if (StringUtils.isEmpty(json) || Objects.isNull(JsonUtils.toJsonNode(json).get("taskId"))) {
            throw TASK_ID_IS_NULL.buildException();
        }
        String taskId = JsonUtils.toJsonNode(json).get("taskId").asText();
        DataResult dataResult = new DataResult();
        dataResult.setList(Lists.newArrayList());
        List<ShiftHandover> shiftHandovers = shiftHandoverRepository.findByTaskTaskIdOrderByHandoverTimeAsc(taskId);
        if (CollectionUtils.isEmpty(shiftHandovers)) return dataResult;
        List<ShiftHandover> inComingHandovers = shiftHandovers.stream().filter(shiftHandover -> Objects.nonNull(shiftHandover.getIncomingTeam()))
                .sorted(Comparator.comparing(ShiftHandover::getHandoverTime))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(inComingHandovers)) return dataResult;
        List<String> teamIds = inComingHandovers.stream().map(shiftHandover -> shiftHandover.getIncomingTeam().getTeamId()).collect(Collectors.toList());
        List<EmployeeTeam> employeeTeams = employeeTeamRepository.findByTeamTeamIdIn(teamIds);
        Map<String, List<EmployeeTeam>> employeeTeamMap;
        if (!CollectionUtils.isEmpty(employeeTeams)) {
            employeeTeamMap = employeeTeams.stream().collect(Collectors.groupingBy(employeeTeam -> employeeTeam.getTeam().getTeamId()));
        } else {
            employeeTeamMap = new HashMap<>();
        }
        AtomicInteger num = new AtomicInteger(1);
        List<TeamInfoDTO> teamInfos = inComingHandovers.stream().map(shiftHandover -> {
            Team incomingTeam = shiftHandover.getIncomingTeam();
            TeamInfoDTO teamInfoDTO = new TeamInfoDTO();
            teamInfoDTO.setNum(num.get());
            teamInfoDTO.setTeamId(incomingTeam.getTeamId());
            teamInfoDTO.setTeamName(incomingTeam.getTeamName());
            teamInfoDTO.setHandoverTime(shiftHandover.getHandoverTime());
            num.getAndIncrement();
            List<EmployeeTeam> employeeTeamList = employeeTeamMap.get(incomingTeam.getTeamId());
            if (!CollectionUtils.isEmpty(employeeTeamList)) {
                Map<String, List<EmployeeTeam>> roleMap = employeeTeamList.stream().collect(Collectors.groupingBy(EmployeeTeam::getRole));
                teamInfoDTO.setMembers(CollectionUtils.isEmpty(roleMap.get(TeamRole.member.getCode())) ?
                        null : roleMap.get(TeamRole.member.getCode()).stream()
                        .map(employeeTeam -> employeeTeam.getEmployee().getFullName())
                        .distinct()
                        .collect(Collectors.joining(",")));
                teamInfoDTO.setGroupLeader(CollectionUtils.isEmpty(roleMap.get(TeamRole.leader.getCode())) ?
                        null : roleMap.get(TeamRole.leader.getCode()).stream()
                        .map(employeeTeam -> employeeTeam.getEmployee().getFullName())
                        .distinct()
                        .collect(Collectors.joining(",")));
            }
            return teamInfoDTO;
        }).toList();
        if (!CollectionUtils.isEmpty(teamInfos)) {
            dataResult.setList(DTOConverter.convert(teamInfos, TeamInfoVO.class));
        }
        return dataResult;
    }

    @Override
    public DataResult findContinuousParams(String json) {
        DataResult dataResult = new DataResult();
        dataResult.setList(Lists.newArrayList());
        SearchCondition searchCondition = JsonUtils.fromJson(json, SearchCondition.class);
        List<ProductionTaskDTO> tasks = findAllWithCondition(searchCondition);
        List<WorkStepDTO> workSteps = new ArrayList<>();
        ProductionTaskDTO task;
        if (CollectionUtils.isEmpty(tasks)) {
            return dataResult;
        }
        task = tasks.getFirst();
        //查询设备id
        TaskEquipment taskEquipment = taskEquipmentRepository.findFirstByTaskTaskId(task.getTaskId());
        String equipmentId = Objects.nonNull(taskEquipment) ? taskEquipment.getId().getEquipId() : null;
        List<ContinuousStepDTO> continuousParams = findContinuousParams(task.getTaskId(), task.getProcessId(), equipmentId);
        if (CollectionUtils.isEmpty(continuousParams)) return dataResult;
        dataResult.setList(DTOConverter.convert(continuousParams, ContinuousStepVO.class));
        return dataResult;
    }

    @Override
    public List<ContinuousStepDTO> findContinuousParams(String taskId, String processId, String equipmentId) {
        List<WorkStepDTO> workSteps = findWorkStepByTask(taskId, processId);
        List<String> workStepIds = workSteps.stream().map(WorkStepDTO::getId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(workStepIds)) {
            List<String> stepIds = workSteps.stream().map(WorkStepDTO::getStepId).collect(Collectors.toList());
            //查询点位信息
            List<DataPointInfo> dataPointInfos = new ArrayList<>();
            if (!StringUtils.isEmpty(equipmentId)) {
                dataPointInfos = dataPointInfoService.findByProcessStepIdsAndEquipmentIdAndPointType(stepIds, equipmentId, PointType.actual_point);
            }

            Map<String, List<DataPointInfo>> dataPointMap = dataPointInfos.stream().collect(Collectors.groupingBy(dataPointInfo -> String.format("%s_%s", dataPointInfo.getProcessStep().getId(), dataPointInfo.getStepParameter().getParamName())));
            ImmutableMap<String, WorkStepDTO> workStepMap = Maps.uniqueIndex(workSteps, WorkStepDTO::getId);
            List<ParameterDefinition> parameterDefinitions = parameterDefinitionRepository.findByWorkStepWorkStepIdInAndParamType_ItemCodeIn(workStepIds, Lists.newArrayList(ParamType.CURVE.getCode()));
            if (!CollectionUtils.isEmpty(parameterDefinitions)) {
                List<ContinuousStepDTO> stepDTOS = new ArrayList<>();
                Map<String, List<ParameterDefinition>> parameterMap = parameterDefinitions.stream().collect(Collectors.groupingBy(parameterDefinition -> parameterDefinition.getWorkStep().getWorkStepId()));
                parameterMap.forEach((workStepId, parameters) -> {
                    ContinuousStepDTO stepDTO = new ContinuousStepDTO();
                    WorkStepDTO workStep = workStepMap.get(workStepId);
                    stepDTO.setWorkStepId(workStep.getId());
                    stepDTO.setStepCode(workStep.getStepCode());
                    stepDTO.setWorkStepName(workStep.getWorkStepName());
                    stepDTO.setStepOrder(workStep.getWorkStepOrder());
                    stepDTO.setStartTime(workStep.getStartTime());
                    stepDTO.setEndTime(workStep.getEndTime());
                    List<ContinuousParamsDTO> paramsDTOS = parameters.stream().map(parameterDefinition -> {
                        List<DataPointInfo> dataPoints = dataPointMap.get(String.format("%s_%s", workStep.getStepId(), parameterDefinition.getParamName()));

                        ContinuousParamsDTO continuousParams = new ContinuousParamsDTO();
                        continuousParams.setStepParam(parameterDefinition.getParamName());
                        continuousParams.setId(parameterDefinition.getParamDefId());
                        if (!CollectionUtils.isEmpty(dataPoints)) {
                            continuousParams.setDataPointCode(dataPoints.stream().findFirst().get().getDataPoint());
                        }
                        return continuousParams;
                    }).collect(Collectors.toList());
                    stepDTO.setContinuousParams(paramsDTOS);
                    stepDTOS.add(stepDTO);
                });
                return stepDTOS;
            }
        }
        return Lists.newArrayList();
    }

    @Override
    public DataResult findAbnormalRecords(String json) {
        if (StringUtils.isEmpty(json) || Objects.isNull(JsonUtils.toJsonNode(json).get("taskId"))) {
            throw TASK_ID_IS_NULL.buildException();
        }
        DataResult dataResult = new DataResult();
        dataResult.setList(Lists.newArrayList());
        String taskId = JsonUtils.toJsonNode(json).get("taskId").asText();
        List<AbnormalRecord> records = abnormalRecordRepository.findByTaskIdOrderByAlertTimeDesc(taskId);
        if (CollectionUtils.isEmpty(records)) return dataResult;
        List<AbnormalRecordDTO> abnormalRecordDTOS = records.stream().map(record -> {
            AbnormalRecordDTO abnormalRecordDTO = new AbnormalRecordDTO();
            abnormalRecordDTO.setAlertTime(record.getAlertTime());
            abnormalRecordDTO.setAlertType(BooleanUtils.isTrue(record.getAlertType()) ? manual.getMsg() : system.getMsg());
            AlarmContent alarmContent = record.getAlarmContent();
            abnormalRecordDTO.setAlarmContent(String.format("工步-%s，“%s”%s;优化建议：%s", alarmContent.getWorkStepName(), alarmContent.getParameterName(), alarmContent.getAlarmContent(), alarmContent.getSuggestion()));
            return abnormalRecordDTO;
        }).toList();
        dataResult.setList(DTOConverter.convert(abnormalRecordDTOS, AbnormalRecordVO.class));
        return dataResult;
    }

    @Override
    public Object fetchPointData(Object json, PointMethodType pointMethodType) {
        Object result = null;
        log.info("查询点位数据，请求参数：{}", json);
        log.info("查询点位数据，调用接口：{}", pointMethodType.getMsg());
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        result = switch (pointMethodType) {
            case batch_point, single_point -> dataPointService.batchFindHistory(json);
            case batch_concurrent_point -> dataPointService.batchList(json);
            case first_point_in_range_date -> dataPointService.firstDetail(json);
            case latest_point_in_range_date -> dataPointService.lastDetail(json);
            case max_point_in_range_date -> dataPointService.maxDetail(json);
            case min_point_in_range_date -> dataPointService.minDetail(json);
            case mean_point_in_range_date -> dataPointService.meanDetail(json);
            case sum_point_in_range_date -> dataPointService.sumDetail(json);
            case end_subtract_start_point -> dataPointService.endSubtractStartDetail(json);
            case spread_point_in_range_date -> dataPointService.spreadDetail(json);
            case upper_lower_point_in_range_date -> dataPointService.upperLowerDetail(json);
            case status_statistics_boolean -> dataPointService.statusStatisticsBooleanDetail(json);
            case end_subtract_start_point_2 -> dataPointService.endSubtractStart2Detail(json);
            case calculator_point_in_range_date -> dataPointService.calculator(json);
        };
        stopWatch.stop();
        log.info("查询点位数据，调用接口：{}，耗时：{}毫秒", pointMethodType.getMsg(), stopWatch.getTotalTimeMillis());
        return result;
    }

    @Override
    public String findTaskStatus(String taskId) {
        SearchCondition condition = new SearchCondition();
        SearchItems searchItems = SearchItems.builder()
                .item(new SearchItem("taskId", taskId, null, SearchItem.Operator.EQ))
                .build();
        condition.setSearchItems(searchItems);
        List<String> openProps = Lists.newArrayList("statusCode");
        condition.setOpenProps(openProps);
        List<ProductionTask> tasks = this.findEntityWithCondition(condition);
        if (CollectionUtils.isEmpty(tasks)) return null;
        SysDictItem statusCode = tasks.getFirst().getStatusCode();
        return statusCode.getItemCode();
    }

    @Override
    public DataResult getScoreDetails(String json, String taskId) {
        if (StringUtils.isEmpty(taskId)) throw TASK_ID_IS_NULL.buildException();
        DataResult dataResult = new DataResult();
        dataResult.setList(Lists.newArrayList());
        SearchCondition searchCondition = JsonUtils.fromJson(json, SearchCondition.class);
        SearchItems searchItems = searchCondition.getSearchItems();
        if (Objects.isNull(searchItems)) {
            searchCondition.setSearchItems(SearchItems.builder()
                    .item(new SearchItem("task.id", taskId, null, SearchItem.Operator.EQ))
                    .build());
        } else {
            searchItems.addItem(new SearchItem("task.id", taskId, null, SearchItem.Operator.EQ));
        }
        PageResult<ProcessScoreDetailsDTO> result = processScoreDetailsService.findByCondition(searchCondition);
        dataResult.setTotal(result.getTotal());
        dataResult.setPageSize(result.getPageSize());
        dataResult.setPageCurrent(result.getPageCurrent());
        if (CollectionUtils.isEmpty(result.getContent())) {
            dataResult.setList(Lists.newArrayList());
            return dataResult;
        }
        List<List<Object>> value = DTOConverter.convert(result.getContent(), ProcessScoreDetailsVO.class);
        dataResult.setList(value);
        return dataResult;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTask(String taskId) {
        ProductionTask productionTask = repository.findByTaskId(taskId);
        if (Objects.isNull(productionTask)) {
            log.error("{}:状态更改的任务不存在", taskId);
            throw new RuntimeException(String.format("%s:任务撤销的任务不存在", taskId));
        }

        deleteTaskExtend(productionTask.getTaskId());
        deleteTaskEquipment(productionTask.getTaskId());
        deleteWorkStep(productionTask.getTaskId());

        this.delete(productionTask.getTaskId());
        log.info("{}:删除成功", productionTask.getTaskCode());
    }

    private void deleteTaskExtend(String taskId) {
        productionTaskExtendRepository.deleteAllByTaskTaskIdIn(Lists.newArrayList(taskId));
    }

    private void deleteTaskEquipment(String taskId) {
        //删除设备关联关系
        taskEquipmentRepository.deleteAllByTaskTaskIdIn(Lists.newArrayList(taskId));
    }

    private void deleteWorkStep(String taskId) {
        //删除工步关联关系
        List<WorkStep> workSteps = workStepRepository.findByTaskTaskId(taskId);
        if (!CollectionUtils.isEmpty(workSteps)) {
            // 先删除参数定义
            List<String> workStepIds = workSteps.stream()
                    .map(WorkStep::getWorkStepId)
                    .collect(Collectors.toList());
            deleteParameterDefinitions(workStepIds);

            // 再删除工步
            workStepRepository.deleteAll(workSteps);
        }
    }

    /**
     * 删除参数定义及其关联数据
     * @param workStepIds 工步ID列表
     */
    private void deleteParameterDefinitions(List<String> workStepIds) {
        if (CollectionUtils.isEmpty(workStepIds)) {
            return;
        }

        // 查询需要删除的参数定义
        List<ParameterDefinition> parameterDefinitions = parameterDefinitionRepository
                .findByWorkStepWorkStepIdIn(workStepIds);

        if (CollectionUtils.isEmpty(parameterDefinitions)) {
            log.info("工步 {} 下没有找到需要删除的参数定义", workStepIds);
            return;
        }

        List<String> paramDefIds = parameterDefinitions.stream()
                .map(ParameterDefinition::getParamDefId)
                .collect(Collectors.toList());

        // 删除参数定义扩展信息
        deleteParameterDefinitionExtends(paramDefIds);

        // 删除工艺参数
        deleteProcessParameters(paramDefIds);

        // 删除参数定义
        parameterDefinitionRepository.deleteAll(parameterDefinitions);
    }

    /**
     * 删除参数定义扩展信息
     * @param paramDefIds 参数定义ID列表
     */
    private void deleteParameterDefinitionExtends(List<String> paramDefIds) {
        if (CollectionUtils.isEmpty(paramDefIds)) {
            return;
        }

        parameterDefinitionExtendRepository
                .deleteAllByParamDefIdIn(paramDefIds);

    }

    /**
     * 删除工艺参数
     * @param paramDefIds 参数定义ID列表
     */
    private void deleteProcessParameters(List<String> paramDefIds) {
        if (CollectionUtils.isEmpty(paramDefIds)) {
            return;
        }

        processParameterRepository
                .deleteAllByParamDefParamDefIdIn(paramDefIds);

    }

    @Override
    public void changeStatus(String taskId, SysDictItem statusDict) {
        SearchCondition searchCondition = new SearchCondition();
        searchCondition.setOpenProps(Lists.newArrayList("statusCode"));
        searchCondition.setSearchItems(SearchItems.builder()
                .item(new SearchItem("taskId", taskId, null, SearchItem.Operator.EQ))
                .build());
        searchCondition.setOpenProps(Lists.newArrayList("statusCode"));
        List<ProductionTask> tasks = this.findEntityWithCondition(searchCondition);
        if (CollectionUtils.isEmpty(tasks)) throw TASK_NOT_EXIST.buildException(taskId);
        ProductionTask productionTask = tasks.stream().findFirst().get();
//        String statusCode = productionTask.getStatusCode().getItemCode();
//        TaskStatus taskStatus = TaskStatus.valueOf(statusCode);
//        TaskStatus changeStatus = TaskStatus.valueOf(statusDict.getItemCode());

        // 获取变更前的状态信息用于事件发布
        SysDictItem oldStatus = productionTask.getStatusCode();

//        if(taskStatus.getCode().equals(changeStatus.getCode())) {
//            throw STATUS_CHANGE_ERROR.buildException(taskStatus.getMsg(),changeStatus.getMsg());
//        }

        /*switch (taskStatus){
            case TaskStatus.not_start:
                if (!changeStatus.equals(TaskStatus.in_progress)) {
                    throw STATUS_CHANGE_ERROR.buildException(taskStatus.getMsg(),changeStatus.getMsg());
                }
                break;
            case TaskStatus.in_progress:
                if (!changeStatus.equals(TaskStatus.completed)) {
                    throw STATUS_CHANGE_ERROR.buildException(taskStatus.getMsg(),changeStatus.getMsg());
                }
                break;
            case TaskStatus.completed:
                throw STATUS_CHANGE_ERROR.buildException(taskStatus.getMsg(),changeStatus.getMsg());
            default:break;
        }*/

        // 执行状态更新
        repository.changeStatus(productionTask.getTaskId(), statusDict);

        // 发布状态变更事件
        try {
            taskStatusEventPublisher.publishTaskStatusChangeEvent(
                    productionTask.getTaskId(),
                    productionTask.getTaskCode(),
                    oldStatus,
                    statusDict
            );
        } catch (Exception e) {
            log.error("发布任务状态变更事件失败，但状态更新已成功: taskCode={}, oldStatus={}, newStatus={}",
                    productionTask.getTaskCode(),
                    oldStatus != null ? oldStatus.getItemCode() : null,
                    statusDict.getItemCode(), e);
        }
    }

    @Override
    public List<ProductionTask> findByTaskCodes(Collection<String> taskCodes) {
        SearchCondition searchCondition = new SearchCondition();
        searchCondition.setSearchItems(SearchItems.builder()
                .item(new SearchItem("taskCode", taskCodes, null, SearchItem.Operator.IN)).build());

        return findEntityWithCondition(searchCondition);
    }


    /**
     * 重写 findByCondition 方法来处理集合查询
     * 先检查是否包含集合查询，如果包含则直接使用 JPQL，避免事务回滚问题
     */
    @Override
    public PageResult<ProductionTaskDTO> findCondition(SearchCondition condition) {
        // 预检查是否包含集合查询
        if (containsCollectionQuery(condition)) {
            log.info("检测到集合查询，直接使用 JPQL 查询");
            return handleCollectionQueryWithJPQL(condition);
        } else {
            // 使用默认的 QueryDSL 方法
            return findByCondition(condition);
        }
    }

    /**
     * 检查查询条件是否包含集合查询
     */
    private boolean containsCollectionQuery(SearchCondition condition) {
        if (condition.getSearchItems() == null || condition.getSearchItems().getItems() == null) {
            return false;
        }

        for (SearchItem item : condition.getSearchItems().getItems()) {
            String fieldName = item.fieldName;
            String[] parts = fieldName.split("\\.");

            if (parts.length >= 2) {
                // 检查是否为已知的集合字段
                if ("taskEquipments".equals(parts[0]) ||
                        "taskMaterials".equals(parts[0])) {
                    return true;
                }
                // 检查 extendAttr.extendAttr.xxx 的情况
                if ("extendAttr".equals(parts[0]) && parts.length >= 3 && "extendAttr".equals(parts[1])) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 检测是否为集合查询异常
     */
    private boolean isCollectionQueryException(Exception e) {
        String message = e.getMessage();
        if (message != null && (message.contains("Plural path") ||
                message.contains("collection") ||
                message.contains("PathException"))) {
            return true;
        }

        // 检查异常链中的原因
        Throwable cause = e.getCause();
        while (cause != null) {
            String causeMessage = cause.getMessage();
            if (causeMessage != null && (causeMessage.contains("Plural path") ||
                    causeMessage.contains("collection") ||
                    causeMessage.contains("PathException"))) {
                return true;
            }
            cause = cause.getCause();
        }

        return false;
    }

    /**
     * 使用 JPQL 处理集合查询
     * 使用只读事务
     */
    @Transactional(readOnly = true)
    public PageResult<ProductionTaskDTO> handleCollectionQueryWithJPQL(SearchCondition condition) {
        StringBuilder jpqlBuilder = new StringBuilder("SELECT DISTINCT t FROM ProductionTask t");
        StringBuilder whereBuilder = new StringBuilder(" WHERE 1 = 1");
        Map<String, Object> parameters = new HashMap<>();
        Set<String> joins = new HashSet<>();

        if (condition.getSearchItems() != null && condition.getSearchItems().getItems() != null) {
            for (int i = 0; i < condition.getSearchItems().getItems().size(); i++) {
                SearchItem item = condition.getSearchItems().getItems().get(i);
                String paramName = "param" + i;

                if (handleCollectionField(item, jpqlBuilder, whereBuilder, parameters, joins, paramName)) {
                    // 集合字段已处理
                } else {
                    // 处理非集合字段
                    whereBuilder.append(" AND ");
                    appendCondition(whereBuilder, "t." + item.fieldName, item.operator, paramName);
                    parameters.put(paramName, getParameterValue(item.value, item.operator));
                }
            }
        }

        String jpql = jpqlBuilder.toString() + whereBuilder.toString();

        // 查询总数
        String countJpql = jpql.replace("SELECT DISTINCT t", "SELECT COUNT(DISTINCT t)");
        TypedQuery<Long> countQuery = getEntityManager().createQuery(countJpql, Long.class);
        parameters.forEach(countQuery::setParameter);
        long total = countQuery.getSingleResult();

        // 查询数据
        jpql += " ORDER BY t.startTime DESC";
        TypedQuery<ProductionTask> dataQuery = getEntityManager().createQuery(jpql, ProductionTask.class);
        parameters.forEach(dataQuery::setParameter);
        dataQuery.setFirstResult((condition.getPageCurrent() - 1) * condition.getPageSize());
        dataQuery.setMaxResults(condition.getPageSize());

        List<ProductionTask> tasks = dataQuery.getResultList();
        List<ProductionTaskDTO> taskDTOs = convertToDtoList(tasks, ProductionTaskDTO.class);

        return PageResult.of(condition, total, taskDTOs);
    }

    /**
     * 处理集合字段查询
     */
    private boolean handleCollectionField(SearchItem item, StringBuilder jpqlBuilder,
                                          StringBuilder whereBuilder, Map<String, Object> parameters,
                                          Set<String> joins, String paramName) {
        String fieldName = item.fieldName;
        String[] parts = fieldName.split("\\.");

        if (parts.length < 2) {
            return false; // 不是嵌套字段
        }

        // 处理 taskEquipments.equip.xxx
        if ("taskEquipments".equals(parts[0]) && parts.length >= 3) {
            if (!joins.contains("taskEquipments")) {
                jpqlBuilder.append(" JOIN t.taskEquipments te");
                joins.add("taskEquipments");
            }
            if ("equip".equals(parts[1]) && !joins.contains("equip")) {
                jpqlBuilder.append(" JOIN te.equip e");
                joins.add("equip");
            }

            String fieldPath = "e." + parts[2];
            whereBuilder.append(" AND ");
            appendCondition(whereBuilder, fieldPath, item.operator, paramName);
            parameters.put(paramName, getParameterValue(item.value, item.operator));
            return true;
        }

        // 处理 taskMaterials.material.xxx
        else if ("taskMaterials".equals(parts[0]) && parts.length >= 3) {
            if (!joins.contains("taskMaterials")) {
                jpqlBuilder.append(" JOIN t.taskMaterials tm");
                joins.add("taskMaterials");
            }
            if ("material".equals(parts[1]) && !joins.contains("material")) {
                jpqlBuilder.append(" JOIN tm.material m");
                joins.add("material");
            }

            String fieldPath = "m." + parts[2];
            whereBuilder.append(" AND ");
            appendCondition(whereBuilder, fieldPath, item.operator, paramName);
            parameters.put(paramName, getParameterValue(item.value, item.operator));
            return true;
        }

        // 处理 extendAttr.xxx
        else if ("extendAttr".equals(parts[0]) && parts.length >= 3) {
            if (!joins.contains("extendAttr")) {
                jpqlBuilder.append(" JOIN t.extendAttr ea");
                joins.add("extendAttr");
            }

            // 处理 extendAttr.extendAttr.fieldName 的情况
            // parts[1] 应该是 "extendAttr"，parts[2] 是实际的 JSON 字段名
            if ("extendAttr".equals(parts[1]) && parts.length >= 3) {
                String jsonPath = parts[2];
                whereBuilder.append(" AND ");
                appendJsonCondition(whereBuilder, "ea.extendAttr", jsonPath, item.operator, paramName);
                parameters.put(paramName, getParameterValue(item.value, item.operator));
                return true;
            }
        }

        return false; // 不是已知的集合字段
    }

    /**
     * 根据操作符添加查询条件
     */
    private void appendCondition(StringBuilder whereBuilder, String fieldPath,
                                 SearchItem.Operator operator, String paramName) {
        switch (operator) {
            case EQ:
                whereBuilder.append(fieldPath).append(" = :").append(paramName);
                break;
            case NEQ:
                whereBuilder.append(fieldPath).append(" != :").append(paramName);
                break;
            case LIKE:
                whereBuilder.append(fieldPath).append(" LIKE :").append(paramName);
                break;
            case GT:
                whereBuilder.append(fieldPath).append(" > :").append(paramName);
                break;
            case GTE:
                whereBuilder.append(fieldPath).append(" >= :").append(paramName);
                break;
            case LT:
                whereBuilder.append(fieldPath).append(" < :").append(paramName);
                break;
            case LTE:
                whereBuilder.append(fieldPath).append(" <= :").append(paramName);
                break;
            case IN:
                whereBuilder.append(fieldPath).append(" IN :").append(paramName);
                break;
            case NOTIN:
                whereBuilder.append(fieldPath).append(" NOT IN :").append(paramName);
                break;
            default:
                whereBuilder.append(fieldPath).append(" = :").append(paramName);
                break;
        }
    }

    /**
     * 根据操作符获取参数值
     */
    private Object getParameterValue(Object value, SearchItem.Operator operator) {
        if (operator == SearchItem.Operator.LIKE) {
            return "%" + value + "%";
        }
        return value;
    }

    /**
     * 处理 JSON 字段查询条件
     * 针对达梦数据库优化 JSON 查询语法
     */
    private void appendJsonCondition(StringBuilder whereBuilder, String jsonField, String jsonPath,
                                     SearchItem.Operator operator, String paramName) {
        switch (operator) {
            case EQ:
                whereBuilder.append("JSON_EXTRACT(").append(jsonField).append(", '$.").append(jsonPath).append("') = :").append(paramName);
                break;
            case NEQ:
                whereBuilder.append("JSON_EXTRACT(").append(jsonField).append(", '$.").append(jsonPath).append("') != :").append(paramName);
                break;
            case LIKE:
                // 达梦数据库：使用 TO_CHAR 函数将 JSON 值转换为字符串
                whereBuilder.append("TO_CHAR(JSON_EXTRACT(").append(jsonField).append(", '$.").append(jsonPath).append("')) LIKE :").append(paramName);
                break;
            case GT:
                // 达梦数据库：使用 TO_NUMBER 函数将 JSON 值转换为数值
                whereBuilder.append("TO_NUMBER(JSON_EXTRACT(").append(jsonField).append(", '$.").append(jsonPath).append("')) > :").append(paramName);
                break;
            case GTE:
                whereBuilder.append("TO_NUMBER(JSON_EXTRACT(").append(jsonField).append(", '$.").append(jsonPath).append("')) >= :").append(paramName);
                break;
            case LT:
                whereBuilder.append("TO_NUMBER(JSON_EXTRACT(").append(jsonField).append(", '$.").append(jsonPath).append("')) < :").append(paramName);
                break;
            case LTE:
                whereBuilder.append("TO_NUMBER(JSON_EXTRACT(").append(jsonField).append(", '$.").append(jsonPath).append("')) <= :").append(paramName);
                break;
            case IN:
                whereBuilder.append("JSON_EXTRACT(").append(jsonField).append(", '$.").append(jsonPath).append("') IN :").append(paramName);
                break;
            case NOTIN:
                whereBuilder.append("JSON_EXTRACT(").append(jsonField).append(", '$.").append(jsonPath).append("') NOT IN :").append(paramName);
                break;
            default:
                whereBuilder.append("JSON_EXTRACT(").append(jsonField).append(", '$.").append(jsonPath).append("') = :").append(paramName);
                break;
        }
    }

    @Override
    public ProductionTask findOneByTaskCode(String taskCode) {
        SearchCondition searchCondition = new SearchCondition();
        searchCondition.setSearchItems(SearchItems.builder().item(new SearchItem("taskCode", taskCode, null, SearchItem.Operator.EQ)).build());
        return findOne(searchCondition);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProductionTask findByTaskIdWithProcess(String taskId) {
        SearchCondition searchCondition = new SearchCondition();
        searchCondition.setOpenProps(Lists.newArrayList("process"));
        searchCondition.setSearchItems(SearchItems.builder().item(new SearchItem("taskId", taskId, null, SearchItem.Operator.EQ)).build());
        return findOne(searchCondition);
    }

    @Override
    public List<ResultParamsDTO> findResultParams(String taskId, String processId) {
        List<WorkStepDTO> workSteps = new ArrayList<>();
        workSteps = findWorkStepByTask(taskId, processId);
        List<String> workStepIds = workSteps.stream().map(WorkStepDTO::getId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(workStepIds)) {
            ImmutableMap<String, WorkStepDTO> workStepMap = Maps.uniqueIndex(workSteps, WorkStepDTO::getId);
            List<ParameterDefinition> parameterDefinitions = parameterDefinitionRepository.findByWorkStepWorkStepIdInAndParamType_ItemCodeIn(workStepIds, Lists.newArrayList(ParamType.NUMBER.getCode(), ParamType.TEXT.getCode(), ParamType.DATE.getCode()));
            if (!CollectionUtils.isEmpty(parameterDefinitions)) {
                List<String> paramDefIds = parameterDefinitions.stream().map(ParameterDefinition::getParamDefId).collect(Collectors.toList());
                List<ProcessParameter> processParameters = processParameterService.findByParamDefAndTask(paramDefIds, workSteps.stream().findFirst().get().getTaskId());
                Map<String, List<ProcessParameter>> parameterMap = processParameters.stream().collect(Collectors.groupingBy(processParameter -> processParameter.getParamDef().getParamDefId()));
                return parameterDefinitions.stream().map(parameterDefinition -> {
                    ResultParamsDTO resultParamsDTO = new ResultParamsDTO();
                    WorkStepDTO workStep = workStepMap.get(parameterDefinition.getWorkStep().getWorkStepId());
                    resultParamsDTO.setId(workStep.getId());
                    resultParamsDTO.setStepCode(workStep.getStepCode());
                    resultParamsDTO.setWorkStepName(workStep.getWorkStepName());
                    resultParamsDTO.setStepOrder(workStep.getWorkStepOrder());
                    resultParamsDTO.setStepParam(parameterDefinition.getParamName());
                    resultParamsDTO.setMin(parameterDefinition.getMinValue());
                    resultParamsDTO.setMax(parameterDefinition.getMaxValue());
                    resultParamsDTO.setStandard(String.format("%s~%s %s", Objects.isNull(parameterDefinition.getMinValue()) ? "" : parameterDefinition.getMinValue().stripTrailingZeros().toPlainString(),
                            Objects.isNull(parameterDefinition.getMaxValue()) ? "" : parameterDefinition.getMaxValue().stripTrailingZeros().toPlainString(), StringUtils.isNotBlank(parameterDefinition.getUnit()) ? parameterDefinition.getUnit() : ""));
                    List<ProcessParameter> parameters = parameterMap.get(parameterDefinition.getParamDefId());
                    resultParamsDTO.setActualValue("-");
                    if (!CollectionUtils.isEmpty(parameters)) {
                        ProcessParameter parameter = parameters.getFirst();
                        switch (ParamType.valueOf(parameterDefinition.getParamType().getItemCode())) {
                            case NUMBER:
                                ProcessNumParameter processNumParameter = parameter.getProcessNumParameter();
                                if (Objects.nonNull(processNumParameter) && Objects.nonNull(processNumParameter.getActualValue())) {
                                    resultParamsDTO.setActualValue(processNumParameter.getActualValue().stripTrailingZeros().toPlainString());
                                }
                                break;
                            case DATE:
                                ProcessDateParameter processDateParameter = parameter.getProcessDateParameter();
                                if (Objects.nonNull(processDateParameter) && Objects.nonNull(processDateParameter.getActualValue())) {
                                    resultParamsDTO.setActualValue(LocalDateTimeUtil.format(processDateParameter.getActualValue(), "yyyy-MM-dd HH:mm:ss"));
                                }
                                break;
                            case TEXT:
                                ProcessTextParameter processTextParameter = parameter.getProcessTextParameter();
                                if (Objects.nonNull(processTextParameter) && !StringUtils.isEmpty(processTextParameter.getActualValue())) {
                                    resultParamsDTO.setActualValue(processTextParameter.getActualValue());
                                }
                                break;
                            default:
                                break;
                        }
                    }
                    return resultParamsDTO;
                }).collect(Collectors.toList());

            }
        }
        return List.of();
    }

}
