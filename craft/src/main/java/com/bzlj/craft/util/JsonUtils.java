package com.bzlj.craft.util;

import com.bzlj.craft.exception.JsonException;
import com.fasterxml.jackson.core.JacksonException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.node.TextNode;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.io.IOException;
import java.io.InputStream;
import java.util.Map;
import java.util.Optional;

/**
 * Json 工具类
 *
 * <AUTHOR>
 * @date 2025/3/21 10:59
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
public class JsonUtils {
    private static final JsonMapper JSON_MAPPER = newJsonMapper();

    // 禁用构造函数
    private JsonUtils() {
        throw new AssertionError("工具类不可实例化");
    }

    /**
     * 构建自定义配置的 ObjectMapper
     */
    private static JsonMapper newJsonMapper() {
        return JsonMapper.builder()
                // 禁用自动检测 getter 方法（提升性能）
//                .disable(MapperFeature.AUTO_DETECT_GETTERS)
                // 忽略JSON中的未知属性（防止反序列化失败）
                 .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
                // 启用美化输出（开发环境有用，生产环境按需关闭）
                .enable(SerializationFeature.INDENT_OUTPUT)
                // 枚举序列化使用 toString() 而不是 name()
                .enable(SerializationFeature.WRITE_ENUMS_USING_TO_STRING)
                // 支持 Java 8 时间类型（如 LocalDateTime）
                .addModule(new JavaTimeModule())
                .build();

    }

    /**
     * 对象转 JSON 字符串
     */
    @Nullable
    public static String toJson(@Nullable Object obj) {
        return toJson(obj, false);
    }

    /**
     * 对象转 JSON 字符串（美化输出）
     */
    @Nullable
    public static String toPrettyJson(@Nullable Object obj) {
        return toJson(obj, true);
    }

    private static String toJson(@Nullable Object obj, boolean pretty) {
        if (obj == null) return null;
        try {
            return pretty ?
                    JSON_MAPPER.writerWithDefaultPrettyPrinter().writeValueAsString(obj) :
                    JSON_MAPPER.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            throw new JsonException("对象序列化失败", e);
        }
    }

    /**
     * JSON 字符串转对象（支持泛型）
     * 示例：JsonUtils.fromJson(json, new TypeReference<List<User>>() {})
     */
    @NonNull
    public static <T> T fromJson(@NonNull String json, @NonNull TypeReference<T> typeRef) {
        try {
            return JSON_MAPPER.readValue(json, typeRef);
        } catch (JacksonException e) {
            throw new JsonException("JSON解析失败", e);
        }
    }

    /**
     * JSON 字符串转对象（Class形式）
     */
    @NonNull
    public static <T> T fromJson(@NonNull String json, @NonNull Class<T> clazz) {
        try {
            return JSON_MAPPER.readValue(json, clazz);
        } catch (JacksonException e) {
            throw new JsonException("JSON解析失败", e);
        }
    }

    /**
     * 安全解析 JSON 字符串（返回Optional）
     */
    @NonNull
    public static <T> Optional<T> safeParse(@Nullable String json, @NonNull Class<T> clazz) {
        if (json == null || json.isBlank()) return Optional.empty();
        try {
            return Optional.of(JSON_MAPPER.readValue(json, clazz));
        } catch (JacksonException e) {
            return Optional.empty();
        }
    }

    /**
     * 流式解析（适合大文件处理）
     */
    @NonNull
    public static <T> T parseStream(@NonNull InputStream input, @NonNull Class<T> clazz) {
        try {
            return JSON_MAPPER.readValue(input, clazz);
        } catch (IOException e) {
            throw new JsonException("流式解析失败", e);
        }
    }

    /**
     * 转换为 JsonNode
     */
    @NonNull
    public static JsonNode toJsonNode(@NonNull String json) {
        try {
            return JSON_MAPPER.readTree(json);
        } catch (JacksonException e) {
            throw new JsonException("JSON转换失败", e);
        }
    }

    /**
     * 获取 JSON 节点值
     * 示例：JsonUtils.getNodeValue(json, "/user/name", String.class)
     */
    @Nullable
    public static <T> T getNodeValue(@NonNull String json, @NonNull String path, @NonNull Class<T> type) {
        try {
            JsonNode root = JSON_MAPPER.readTree(json);
            JsonNode node = root.at(path);
            return JSON_MAPPER.treeToValue(node, type);
        } catch (JacksonException e) {
            return null;
        }
    }

    /**
     * 对象深拷贝
     */
    @SuppressWarnings("unchecked")
    @NonNull
    public static <T> T deepClone(@NonNull T obj) {
        return fromJson(toJson(obj), (Class<T>) obj.getClass());
    }

    /**
     * 对象转 Map（保留类型信息）
     */
    @NonNull
    public static Map<String, Object> toMap(@NonNull Object obj) {
        try {
            // 验证对象类型，确保是可序列化的对象
            if (obj.getClass().isPrimitive() || obj instanceof Number || obj instanceof Boolean || obj instanceof String) {
                throw new IllegalArgumentException("基本类型或简单对象不能直接转换为Map");
            }
            return JSON_MAPPER.convertValue(obj, new TypeReference<Map<String, Object>>() {});
        } catch (IllegalArgumentException e) {
            throw new JsonException("对象转换失败: " + e.getMessage(), e);
        }
    }

    @NonNull
    public static Map<String, Object> nodeToMap(@NonNull Object obj) {
        try {
            if(obj instanceof TextNode){
                return toMap(toJsonNode(((TextNode) obj).asText()));
            }
            return toMap(obj);
        } catch (IllegalArgumentException e) {
            throw new JsonException("对象转换失败: " + e.getMessage(), e);
        }
    }

}
