package com.bzlj.craft.service;


import cn.hutool.extra.spring.SpringUtil;
import com.bzlj.base.result.DataResult;
import com.bzlj.base.result.PageResult;
import com.bzlj.base.search.SearchCondition;
import com.bzlj.base.search.SearchItem;
import com.bzlj.base.search.SearchItems;
import com.bzlj.craft.api.service.IOperationLogService;
import com.bzlj.craft.api.service.IQualityInspectionService;
import com.bzlj.craft.common.BaseTestNGTest;
import com.bzlj.craft.entity.ProcessStep;
import com.bzlj.craft.entity.ProductionTask;
import com.bzlj.craft.entity.WorkStep;
import com.bzlj.craft.repository.ParameterDefinitionRepository;
import com.bzlj.craft.repository.WorkStepRepository;
import com.bzlj.craft.service.impl.SurveillanceServiceImpl;
import com.bzlj.craft.util.JsonUtils;
import com.google.common.collect.Lists;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.util.AopTestUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.*;


/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-12 10:59
 */
//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class SurveillanceServiceMockTest  extends BaseTestNGTest {

    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    @Spy
    @InjectMocks
    private SurveillanceServiceImpl surveillanceService;
    @Autowired
    JPAQueryFactory queryFactory;


    @BeforeEach
    public void setUp() {

        SurveillanceServiceImpl service =
                AopTestUtils.getUltimateTargetObject(SpringUtil.getBean(SurveillanceServiceImpl.class));
        surveillanceService = spy(service);
        MockitoAnnotations.openMocks(this);
    }


    @Test
    void findByCondition4() {
        SearchCondition searchCondition = new SearchCondition();
        try {
            doReturn(new PageResult<>()).when(surveillanceService).findByCondition(searchCondition);
            surveillanceService.getSurveillanceList(JsonUtils.toJson(searchCondition));

        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }

    @Test
    void findSurveillanceStep() {
        SearchCondition searchCondition = new SearchCondition();
        doReturn(Lists.newArrayList()).when(surveillanceService).findAllWithCondition(searchCondition);
        DataResult surveillanceStep = surveillanceService.findSurveillanceStep(JsonUtils.toJson(searchCondition));
        System.out.println(surveillanceStep);
    }

    @Test
    void findStepResultParams() {
        SearchCondition searchCondition = new SearchCondition();
        doReturn(Lists.newArrayList()).when(surveillanceService).findAllWithCondition(searchCondition);
        DataResult surveillanceStep = surveillanceService.findStepResultParams(JsonUtils.toJson(searchCondition));
        System.out.println(surveillanceStep);
    }

    @Mock
    private ParameterDefinitionRepository parameterDefinitionRepository;


    @Mock
    private WorkStepRepository workStepRepository;

    @Test
    void findStepResultParams1() {
        SearchCondition searchCondition = new SearchCondition();
        searchCondition.setSearchItems(SearchItems.builder()
                .item(new SearchItem("taskId", "TASK001", null, SearchItem.Operator.EQ))
                .build());

        searchCondition.setOpenProps(Lists.newArrayList("process", "taskMaterials", "taskEquipments", "process.craft",
                "taskEquipments.equip", "taskMaterials.material"));


//        when(parameterDefinitionRepository.findByWorkStepWorkStepIdInAndParamType(workStepId, ParamType.number.getCode()))
//                .thenReturn(Lists.newArrayList());
        List<WorkStep> steps = new ArrayList<>();
        WorkStep workStep = new WorkStep();
        workStep.setStep(new ProcessStep());
        workStep.setWorkStepId("WS001");
        workStep.setEndTime(LocalDateTime.now());
        workStep.setWorkStepName("step1");
        workStep.setStartTime(LocalDateTime.now());
        workStep.setWorkStepOrder(1);
        workStep.setStatusCode(null);
        workStep.setTask(new ProductionTask());
        workStep.setWorkStepId("WS001");
        steps.add(workStep);
        when(workStepRepository.findByStepIdInAndTaskTaskIdAndDeleted(Mockito.anyList(), Mockito.anyString(),false))
                .thenReturn(Lists.newArrayList(workStep));
        DataResult surveillanceStep = surveillanceService.findStepResultParams(JsonUtils.toJson(searchCondition));
        System.out.println(surveillanceStep);
    }

    @Mock
    IOperationLogService operationLogService;

    @Test
    void findOperation(){
        SearchCondition searchCondition = new SearchCondition();
        searchCondition.setSearchItems(SearchItems.builder()
                .item(new SearchItem("task.taskId", "TASK001", null, SearchItem.Operator.EQ))
                .build());
        when(operationLogService.findAllWithCondition(Mockito.any()))
                .thenReturn(Lists.newArrayList());
        DataResult surveillanceStep = surveillanceService.findOperation(JsonUtils.toJson(searchCondition));
        System.out.println(surveillanceStep);
    }

    @Mock
    private IQualityInspectionService qualityInspectionService;

    @Test
    void findInspectionData(){
        SearchCondition searchCondition = new SearchCondition();
        searchCondition.setSearchItems(SearchItems.builder()
                .item(new SearchItem("task.taskId", "TASK327002", null, SearchItem.Operator.EQ))
                .build());
        when(qualityInspectionService.findInspectionData(Mockito.any()))
                .thenReturn(new HashMap<>());
        DataResult c = surveillanceService.findInspectionData(JsonUtils.toJson(searchCondition));
        System.out.println(c);
    }

    @Test
    void findInspectionData1(){
        SearchCondition searchCondition = new SearchCondition();
        searchCondition.setSearchItems(SearchItems.builder()
                .item(new SearchItem("task.taskId", "TASK001", null, SearchItem.Operator.EQ))
                .build());
        Map<String,Object> map = new HashMap<>();
        map.put("inspectionData",Lists.newArrayList());
        when(qualityInspectionService.findInspectionData(Mockito.any()))
                .thenReturn(map);
        DataResult c = surveillanceService.findInspectionData(JsonUtils.toJson(searchCondition));
        System.out.println(c);
    }

}