package com.bzlj.message.jpa.repository;

import com.bzlj.message.jpa.entity.Text2Entity;
import com.bzlj.message.jpa.entity.Text2Id;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * Text2 表只读 Repository
 * 只提供查询功能，过滤 tc_no 或 tc_item_name 为空的记录
 *
 * <AUTHOR>
 * @date 2025/8/2
 */
@Repository
@ConditionalOnProperty(name = "postgresql.enabled", havingValue = "true")
public interface Text2Repository extends JpaRepository<Text2Entity, Text2Id> {

    // 过滤条件：排除 tc_no 或 tc_item_name 为空的记录
    String FILTER_CONDITION = "NOT (t.tcNo IS NULL OR t.tcNo = '' OR t.tcItemName IS NULL OR t.tcItemName = '')";
    String ORDER_BY = "ORDER BY t.recCreateTime, t.tcNo, t.tcItemSeqNo";

    /**
     * 统计有效记录总数
     */
    @Query("SELECT COUNT(t) FROM Text2Entity t WHERE " + FILTER_CONDITION)
    long countAll();

    /**
     * 分页查询有效记录
     */
    @Query("SELECT t FROM Text2Entity t WHERE " + FILTER_CONDITION + " " + ORDER_BY)
    Page<Text2Entity> findAllOrderByCreateTimeAndSeq(Pageable pageable);

    /**
     * 根据创建时间分页查询有效记录
     */
    @Query("SELECT t FROM Text2Entity t WHERE t.recCreateTime > :createTime AND " + FILTER_CONDITION + " " + ORDER_BY)
    Page<Text2Entity> findByRecCreateTimeAfter(@Param("createTime") String createTime, Pageable pageable);

    /**
     * 统计指定时间后的有效记录数
     */
    @Query("SELECT COUNT(t) FROM Text2Entity t WHERE t.recCreateTime > :createTime AND " + FILTER_CONDITION)
    long countByRecCreateTimeAfter(@Param("createTime") String createTime);

    // 只读模式：重写写操作方法，防止意外调用

    @Deprecated
    @Override
    default <S extends Text2Entity> S save(S entity) {
        throw new UnsupportedOperationException("当前数据源为只读模式，不支持保存操作");
    }

    @Deprecated
    @Override
    default void deleteById(Text2Id id) {
        throw new UnsupportedOperationException("当前数据源为只读模式，不支持删除操作");
    }

    @Deprecated
    @Override
    default void delete(Text2Entity entity) {
        throw new UnsupportedOperationException("当前数据源为只读模式，不支持删除操作");
    }
}
