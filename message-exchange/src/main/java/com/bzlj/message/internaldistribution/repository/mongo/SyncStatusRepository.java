package com.bzlj.message.internaldistribution.repository.mongo;

import com.bzlj.message.internaldistribution.entity.mongo.SyncStatus;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 同步状态 Repository
 *
 * <AUTHOR>
 * @date 2025/8/2
 */
@Repository
public interface SyncStatusRepository extends MongoRepository<SyncStatus, String> {

    /**
     * 查找最后一次成功的同步记录
     *
     * @param syncType 同步类型
     * @return 最后一次成功的同步记录
     */
    @Query("{'sync_type': ?0, 'sync_status': 'SUCCESS'}")
    Optional<SyncStatus> findLastSuccessfulSync(String syncType);

    /**
     * 查找最后一次成功的增量同步记录（按开始时间倒序）
     *
     * @return 最后一次成功的增量同步记录
     */
    @Query(value = "{'sync_type': 'INCREMENTAL_SYNC', 'sync_status': 'SUCCESS'}", sort = "{'start_time': -1}")
    Optional<SyncStatus> findLastSuccessfulIncrementalSync();

    /**
     * 查找最后一次成功的全量同步记录（按开始时间倒序）
     *
     * @return 最后一次成功的全量同步记录
     */
    @Query(value = "{'sync_type': 'FULL_SYNC', 'sync_status': 'SUCCESS'}", sort = "{'start_time': -1}")
    Optional<SyncStatus> findLastSuccessfulFullSync();

    /**
     * 根据同步类型查找所有记录，按开始时间倒序
     *
     * @param syncType 同步类型
     * @return 同步记录列表
     */
    List<SyncStatus> findBySyncTypeOrderByStartTimeDesc(String syncType);

    /**
     * 根据同步状态查找记录
     *
     * @param syncStatus 同步状态
     * @return 同步记录列表
     */
    List<SyncStatus> findBySyncStatus(String syncStatus);

    /**
     * 查找正在运行的同步任务
     *
     * @return 正在运行的同步任务列表
     */
    @Query("{'sync_status': 'RUNNING'}")
    List<SyncStatus> findRunningSyncTasks();

    /**
     * 检查是否有正在运行的同步任务
     *
     * @return 是否有正在运行的任务
     */
    @Query(value = "{'sync_status': 'RUNNING'}", count = true)
    long countRunningSyncTasks();
}
