# 同步服务改进总结

## 问题描述

原有的增量同步逻辑存在以下问题：

1. **只查询增量同步记录**：`getLastSyncTime()` 方法只查找 `INCREMENTAL_SYNC` 类型的记录，忽略了全量同步记录
2. **缺少排序**：查询没有按 `startTime` 排序，可能返回的不是最新的记录
3. **未设置 lastSyncCreateTime**：在 `completeSyncStatus` 方法中没有设置 `lastSyncCreateTime` 字段
4. **只考虑创建时间**：增量同步只考虑了 `recCreateTime`，没有考虑 `recReviseTime`（更新时间）

## 解决方案

### 1. 修改 SyncStatusRepository

**文件**: `message-exchange/src/main/java/com/bzlj/message/internaldistribution/repository/mongo/SyncStatusRepository.java`

**修改内容**:
- 为 `findLastSuccessfulIncrementalSync()` 和 `findLastSuccessfulFullSync()` 方法添加排序
- 使用 `sort = "{'start_time': -1}"` 确保返回最新的记录

```java
@Query(value = "{'sync_type': 'INCREMENTAL_SYNC', 'sync_status': 'SUCCESS'}", sort = "{'start_time': -1}")
Optional<SyncStatus> findLastSuccessfulIncrementalSync();

@Query(value = "{'sync_type': 'FULL_SYNC', 'sync_status': 'SUCCESS'}", sort = "{'start_time': -1}")
Optional<SyncStatus> findLastSuccessfulFullSync();
```

### 2. 修改 Text2Repository

**文件**: `message-exchange/src/main/java/com/bzlj/message/jpa/repository/Text2Repository.java`

**修改内容**:
- 添加新的查询方法，同时考虑创建时间和更新时间

```java
@Query("SELECT t FROM Text2Entity t WHERE (t.recCreateTime > :syncTime OR t.recReviseTime > :syncTime) AND " + FILTER_CONDITION + " " + ORDER_BY)
Page<Text2Entity> findByCreateTimeOrReviseTimeAfter(@Param("syncTime") String syncTime, Pageable pageable);

@Query("SELECT COUNT(t) FROM Text2Entity t WHERE (t.recCreateTime > :syncTime OR t.recReviseTime > :syncTime) AND " + FILTER_CONDITION)
long countByCreateTimeOrReviseTimeAfter(@Param("syncTime") String syncTime);
```

### 3. 修改 ParallelSyncService

**文件**: `message-exchange/src/main/java/com/bzlj/message/sync/service/ParallelSyncService.java`

#### 3.1 改进 getLastSyncTime() 方法

**修改内容**:
- 同时查询增量同步和全量同步记录
- 比较两者的 `startTime`，返回最新的同步时间
- 如果 `lastSyncCreateTime` 为空，则使用 `startTime`

```java
private String getLastSyncTime() {
    // 获取最后一次成功的增量同步记录
    Optional<SyncStatus> lastIncrementalSync = syncStatusRepository.findLastSuccessfulIncrementalSync();
    
    // 获取最后一次成功的全量同步记录
    Optional<SyncStatus> lastFullSync = syncStatusRepository.findLastSuccessfulFullSync();
    
    // 比较两者的 startTime，获取最新的同步记录
    SyncStatus lastSync = null;
    
    if (lastIncrementalSync.isPresent() && lastFullSync.isPresent()) {
        // 两者都存在，比较 startTime
        SyncStatus incrementalSync = lastIncrementalSync.get();
        SyncStatus fullSync = lastFullSync.get();
        
        if (incrementalSync.getStartTime().isAfter(fullSync.getStartTime())) {
            lastSync = incrementalSync;
        } else {
            lastSync = fullSync;
        }
    } else if (lastIncrementalSync.isPresent()) {
        // 只有增量同步记录
        lastSync = lastIncrementalSync.get();
    } else if (lastFullSync.isPresent()) {
        // 只有全量同步记录
        lastSync = lastFullSync.get();
    }
    
    // 返回 lastSyncCreateTime，如果为空则返回 startTime 的字符串形式
    if (lastSync != null) {
        String lastSyncCreateTime = lastSync.getLastSyncCreateTime();
        if (lastSyncCreateTime != null && !lastSyncCreateTime.trim().isEmpty()) {
            return lastSyncCreateTime;
        } else {
            // 如果 lastSyncCreateTime 为空，使用 startTime
            return lastSync.getStartTime().toString();
        }
    }
    
    return null;
}
```

#### 3.2 改进 completeSyncStatus() 方法

**修改内容**:
- 设置 `lastSyncCreateTime` 字段，用于下次增量同步的基准时间

```java
private SyncStatus completeSyncStatus(SyncStatus syncStatus, long successCount, long failedCount,
                                    long insertedCount, long updatedCount, long skippedCount, String message) {
    LocalDateTime endTime = LocalDateTime.now();
    long duration = java.time.Duration.between(syncStatus.getStartTime(), endTime).toMillis();

    // 设置最后同步的创建时间，用于下次增量同步的基准时间
    String lastSyncCreateTime = getCurrentTimeString();

    syncStatus.setSuccessRecords(successCount)
             .setFailedRecords(failedCount)
             .setInsertedRecords(insertedCount)
             .setUpdatedRecords(updatedCount)
             .setSkippedRecords(skippedCount)
             .setSyncStatus("SUCCESS")
             .setEndTime(endTime)
             .setDurationMs(duration)
             .setUpdateTime(endTime)
             .setLastSyncCreateTime(lastSyncCreateTime)  // 新增这一行
             .setRemark(message + String.format(" - 成功: %d, 失败: %d, 新增: %d, 更新: %d, 跳过: %d",
                     successCount, failedCount, insertedCount, updatedCount, skippedCount));

    return syncStatusRepository.save(syncStatus);
}
```

#### 3.3 改进增量同步查询逻辑

**修改内容**:
- 使用新的查询方法，同时考虑创建时间和更新时间

```java
// 在 performParallelIncrementalSync() 方法中
if (lastSyncTime != null) {
    // 使用专门的统计方法，统计创建时间或更新时间在lastSyncTime之后的记录
    totalRecords = text2Repository.countByCreateTimeOrReviseTimeAfter(lastSyncTime);
} else {
    totalRecords = text2Repository.countAll();
}

// 在 processPageForIncrementalSync() 方法中
if (lastSyncTime != null) {
    // 查询创建时间或更新时间在lastSyncTime之后的记录
    page = text2Repository.findByCreateTimeOrReviseTimeAfter(lastSyncTime, pageable);
} else {
    page = text2Repository.findAllOrderByCreateTimeAndSeq(pageable);
}
```

## 改进效果

1. **更准确的同步基准时间**：现在会比较增量同步和全量同步记录，获取真正的最后同步时间
2. **完整的增量同步**：现在会同步在上次同步时间之后创建或更新的所有记录
3. **正确的时间记录**：每次同步完成后会正确设置 `lastSyncCreateTime` 字段
4. **更好的排序保证**：查询结果按时间排序，确保获取最新记录

## 测试

创建了 `ParallelSyncServiceTest` 测试类，验证 `getLastSyncTime()` 方法的各种场景：

- 同时存在增量和全量同步记录时的处理
- 只存在一种同步记录时的处理
- 没有同步记录时的处理
- `lastSyncCreateTime` 为空时的回退逻辑

## 注意事项

1. 这些修改是向后兼容的，不会影响现有的同步逻辑
2. 新的查询条件 `(t.recCreateTime > :syncTime OR t.recReviseTime > :syncTime)` 确保不会遗漏任何需要同步的记录
3. 建议在生产环境部署前进行充分测试，特别是验证增量同步的正确性
